/// Workout Program Page - GymKod Pro Mobile
///
/// Bu sayfa üyelerin antrenman programlarını görüntülemesi için oluşturulmuştur.
/// Angular frontend'deki tasarım sistemini takip eder.
///
/// RESPONSIVE DESIGN:
/// - Responsive card layout ve grid system
/// - Responsive typography scaling
/// - Responsive spacing ve padding
/// - Responsive icon sizes ve container dimensions
/// - Responsive workout card layout
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../providers/workout_program_provider.dart';
import 'workout_program_detail_page.dart';

/// Workout Program Page
/// Member rolündeki kullanıcılar için antrenman programı sayfası
class WorkoutProgramPage extends ConsumerStatefulWidget {
  const WorkoutProgramPage({super.key});

  @override
  ConsumerState<WorkoutProgramPage> createState() => _WorkoutProgramPageState();
}

class _WorkoutProgramPageState extends ConsumerState<WorkoutProgramPage> {
  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında antrenman programını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(workoutProgramProvider.notifier).loadIfNeeded();
      LoggingService.info('Workout program page loaded', tag: 'WORKOUT');
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(

          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Responsive Content Section
                    Expanded(
                      child: Padding(
                        padding: AppSpacing.responsiveScreenPadding(context),
                        child: _buildResponsiveWorkoutContent(theme, deviceType),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }



  /// Responsive Workout Content
  Widget _buildResponsiveWorkoutContent(ThemeData theme, DeviceType deviceType) {
    final isLoading = ref.watch(workoutProgramLoadingProvider);
    final isRefreshing = ref.watch(workoutProgramRefreshingProvider);
    final error = ref.watch(workoutProgramErrorProvider);
    final hasPrograms = ref.watch(hasWorkoutProgramsProvider);
    final programs = ref.watch(activeWorkoutProgramsProvider);

    if (isLoading && !isRefreshing) {
      return _buildLoadingState(theme, deviceType);
    }

    if (error != null) {
      return _buildErrorState(theme, deviceType, error);
    }

    if (!hasPrograms) {
      return _buildNoProgramsState(theme, deviceType);
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(workoutProgramProvider.notifier).refreshWorkoutPrograms();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Program istatistikleri
            _buildProgramStats(theme, deviceType),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),

            // Antrenman programları listesi
            ...programs.map((program) => Padding(
              padding: EdgeInsets.only(
                bottom: AppSpacing.responsive(context,
                  mobile: 12.0,
                  tablet: 16.0,
                  desktop: 20.0,
                ),
              ),
              child: _buildWorkoutProgramCard(theme, deviceType, program),
            )),

            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
          ],
        ),
      ),
    );
  }

  /// Loading durumu
  Widget _buildLoadingState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Antrenman programları yükleniyor...',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Hata durumu
  Widget _buildErrorState(ThemeData theme, DeviceType deviceType, String error) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.error,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Hata Oluştu',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              error,
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                ref.read(workoutProgramProvider.notifier).loadWorkoutPrograms();
              },
              child: const ResponsiveText(
                'Tekrar Dene',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Program yok durumu
  Widget _buildNoProgramsState(ThemeData theme, DeviceType deviceType) {
    return Center(
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 40.0,
                tablet: 48.0,
                desktop: 56.0,
              ),
              color: theme.colorScheme.primary,
            ),
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            ResponsiveText(
              'Henüz Program Atanmamış',
              textType: 'h3',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 10.0,
              desktop: 12.0,
            ),
            ResponsiveText(
              'Size henüz bir antrenman programı atanmamış. Salon yöneticinize danışarak kişisel antrenman programınızı oluşturabilirsiniz.',
              textType: 'bodymedium',
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacing.vertical(
              mobile: 16.0,
              tablet: 20.0,
              desktop: 24.0,
            ),
            ElevatedButton(
              onPressed: () {
                ref.read(workoutProgramProvider.notifier).refreshWorkoutPrograms();
              },
              child: const ResponsiveText(
                'Yenile',
                textType: 'button',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Program istatistikleri kartı
  Widget _buildProgramStats(ThemeData theme, DeviceType deviceType) {
    final stats = ref.watch(workoutProgramStatsProvider);

    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          ResponsiveText(
            'Antrenman Programlarım',
            textType: 'h3',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatItem(
                theme,
                deviceType,
                'Aktif Program',
                stats['activePrograms'].toString(),
                Icons.fitness_center,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// İstatistik item'ı
  Widget _buildStatItem(ThemeData theme, DeviceType deviceType, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: AppSpacing.responsiveIconSize(context,
            mobile: 24.0,
            tablet: 28.0,
            desktop: 32.0,
          ),
          color: theme.colorScheme.primary,
        ),
        ResponsiveSpacing.vertical(
          mobile: 4.0,
          tablet: 6.0,
          desktop: 8.0,
        ),
        ResponsiveText(
          value,
          textType: 'h4',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        ResponsiveText(
          label,
          textType: 'caption',
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Antrenman programı kartı
  Widget _buildWorkoutProgramCard(ThemeData theme, DeviceType deviceType, MemberActiveWorkoutProgramModel program) {
    return GestureDetector(
      onTap: () {
        // Program detay sayfasına git
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => WorkoutProgramDetailPage(
              memberWorkoutProgramId: program.memberWorkoutProgramID,
              programName: program.programName,
            ),
          ),
        );
      },
      child: ResponsiveCard(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Program başlığı ve bilgileri
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      program.programName,
                      textType: 'cardtitle',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (program.programDescription != null) ...[
                      ResponsiveSpacing.vertical(
                        mobile: 4.0,
                        tablet: 6.0,
                        desktop: 8.0,
                      ),
                      ResponsiveText(
                        program.programDescription!,
                        textType: 'bodysmall',
                        maxLines: 2,
                      ),
                    ],
                  ],
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              Text(
                program.targetGoalIcon,
                style: TextStyle(
                  fontSize: AppSpacing.responsiveIconSize(context,
                    mobile: 24.0,
                    tablet: 28.0,
                    desktop: 32.0,
                  ),
                ),
              ),
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),

          // Program detayları
          Row(
            children: [
              // Deneyim seviyesi
              if (program.experienceLevel != null) ...[
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.responsive(context,
                      mobile: 8.0,
                      tablet: 10.0,
                      desktop: 12.0,
                    ),
                    vertical: AppSpacing.responsive(context,
                      mobile: 4.0,
                      tablet: 6.0,
                      desktop: 8.0,
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: Color(int.parse(program.experienceLevelColor.substring(1), radix: 16) + 0xFF000000),
                    borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                      mobile: 12.0,
                      tablet: 14.0,
                      desktop: 16.0,
                    )),
                  ),
                  child: ResponsiveText(
                    program.experienceLevel!,
                    textType: 'caption',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ResponsiveSpacing.horizontal(
                  mobile: 8.0,
                  tablet: 10.0,
                  desktop: 12.0,
                ),
              ],

              // Hedef
              if (program.targetGoal != null) ...[
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppSpacing.responsive(context,
                      mobile: 8.0,
                      tablet: 10.0,
                      desktop: 12.0,
                    ),
                    vertical: AppSpacing.responsive(context,
                      mobile: 4.0,
                      tablet: 6.0,
                      desktop: 8.0,
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                      mobile: 12.0,
                      tablet: 14.0,
                      desktop: 16.0,
                    )),
                  ),
                  child: ResponsiveText(
                    program.targetGoal!,
                    textType: 'caption',
                    style: TextStyle(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),

          // Program süresi bilgisi (sadece bitiş tarihi varsa)
          if (program.remainingDays != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildProgramInfo(
                  theme,
                  deviceType,
                  Icons.schedule,
                  '${program.remainingDays} Gün Kaldı',
                ),
              ],
            ),
          ],

          // Program notları
          if (program.notes != null && program.notes!.isNotEmpty) ...[
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
            Container(
              width: double.infinity,
              padding: AppSpacing.responsiveCardPadding(context),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                  mobile: 8.0,
                  tablet: 10.0,
                  desktop: 12.0,
                )),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveText(
                    'Notlar:',
                    textType: 'labelmedium',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  ResponsiveSpacing.vertical(
                    mobile: 4.0,
                    tablet: 6.0,
                    desktop: 8.0,
                  ),
                  ResponsiveText(
                    program.notes!,
                    textType: 'bodysmall',
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    ),
    );
  }

  /// Program bilgi item'ı
  Widget _buildProgramInfo(ThemeData theme, DeviceType deviceType, IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: AppSpacing.responsiveIconSize(context,
            mobile: 16.0,
            tablet: 18.0,
            desktop: 20.0,
          ),
          color: theme.colorScheme.primary,
        ),
        ResponsiveSpacing.horizontal(
          mobile: 4.0,
          tablet: 6.0,
          desktop: 8.0,
        ),
        Flexible(
          child: ResponsiveText(
            text,
            textType: 'bodysmall',
          ),
        ),
      ],
    );
  }
}
