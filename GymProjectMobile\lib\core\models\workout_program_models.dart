/// Workout Program Models - GymKod Pro Mobile
///
/// Bu dosya backend'deki antrenman programı DTO'larına uygun modeller içerir.
/// Referans: GymProjectBackend/Entities/DTOs/MemberWorkoutProgramDto.cs
library;

import 'package:json_annotation/json_annotation.dart';

part 'workout_program_models.g.dart';

/// Mobil API için aktif antrenman programı modeli
/// Backend: MemberActiveWorkoutProgramDto
@JsonSerializable()
class MemberActiveWorkoutProgramModel {
  /// Program atama ID'si
  final int memberWorkoutProgramID;
  
  /// Program şablon ID'si
  final int workoutProgramTemplateID;
  
  /// Program adı
  final String programName;
  
  /// Program açıklaması
  final String? programDescription;
  
  /// Den<PERSON>im seviyesi (Başlangıç, Orta, İleri)
  final String? experienceLevel;
  
  /// Hedef (Kilo Alma, Kilo Verme, Kas <PERSON>)
  final String? targetGoal;
  
  /// Program başlangıç tarihi
  final DateTime startDate;
  
  /// Program bitiş tarihi
  final DateTime? endDate;
  
  /// Atama notları
  final String? notes;
  
  /// Program gün sayısı
  final int dayCount;
  
  /// Program egzersiz sayısı
  final int exerciseCount;

  const MemberActiveWorkoutProgramModel({
    required this.memberWorkoutProgramID,
    required this.workoutProgramTemplateID,
    required this.programName,
    this.programDescription,
    this.experienceLevel,
    this.targetGoal,
    required this.startDate,
    this.endDate,
    this.notes,
    required this.dayCount,
    required this.exerciseCount,
  });

  /// JSON'dan model oluştur
  factory MemberActiveWorkoutProgramModel.fromJson(Map<String, dynamic> json) =>
      _$MemberActiveWorkoutProgramModelFromJson(json);

  /// Model'i JSON'a çevir
  Map<String, dynamic> toJson() => _$MemberActiveWorkoutProgramModelToJson(this);

  /// Model kopyalama
  MemberActiveWorkoutProgramModel copyWith({
    int? memberWorkoutProgramID,
    int? workoutProgramTemplateID,
    String? programName,
    String? programDescription,
    String? experienceLevel,
    String? targetGoal,
    DateTime? startDate,
    DateTime? endDate,
    String? notes,
    int? dayCount,
    int? exerciseCount,
  }) {
    return MemberActiveWorkoutProgramModel(
      memberWorkoutProgramID: memberWorkoutProgramID ?? this.memberWorkoutProgramID,
      workoutProgramTemplateID: workoutProgramTemplateID ?? this.workoutProgramTemplateID,
      programName: programName ?? this.programName,
      programDescription: programDescription ?? this.programDescription,
      experienceLevel: experienceLevel ?? this.experienceLevel,
      targetGoal: targetGoal ?? this.targetGoal,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      notes: notes ?? this.notes,
      dayCount: dayCount ?? this.dayCount,
      exerciseCount: exerciseCount ?? this.exerciseCount,
    );
  }

  /// Equality
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MemberActiveWorkoutProgramModel &&
        other.memberWorkoutProgramID == memberWorkoutProgramID &&
        other.workoutProgramTemplateID == workoutProgramTemplateID &&
        other.programName == programName;
  }

  @override
  int get hashCode {
    return memberWorkoutProgramID.hashCode ^
        workoutProgramTemplateID.hashCode ^
        programName.hashCode;
  }

  @override
  String toString() {
    return 'MemberActiveWorkoutProgramModel(id: $memberWorkoutProgramID, name: $programName, days: $dayCount, exercises: $exerciseCount)';
  }

  /// Program aktif mi kontrol et
  bool get isActive {
    final now = DateTime.now();
    if (endDate == null) return true;
    return now.isBefore(endDate!) || now.isAtSameMomentAs(endDate!);
  }

  /// Program kaç gün kaldı
  int? get remainingDays {
    if (endDate == null) return null;
    final now = DateTime.now();
    final difference = endDate!.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  /// Deneyim seviyesi rengi (UI için)
  String get experienceLevelColor {
    switch (experienceLevel?.toLowerCase()) {
      case 'başlangıç':
        return '#4CAF50'; // Yeşil
      case 'orta':
        return '#FF9800'; // Turuncu
      case 'ileri':
        return '#F44336'; // Kırmızı
      default:
        return '#9E9E9E'; // Gri
    }
  }

  /// Hedef ikonu (UI için)
  String get targetGoalIcon {
    switch (targetGoal?.toLowerCase()) {
      case 'kilo alma':
        return '📈';
      case 'kilo verme':
        return '📉';
      case 'kas yapma':
        return '💪';
      default:
        return '🎯';
    }
  }
}

/// Antrenman programı listesi response modeli
@JsonSerializable()
class WorkoutProgramListResponse {
  final bool success;
  final String message;
  final List<MemberActiveWorkoutProgramModel> data;

  const WorkoutProgramListResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory WorkoutProgramListResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramListResponseToJson(this);
}

/// Antrenman programı detay response modeli (gelecekte kullanılacak)
@JsonSerializable()
class WorkoutProgramDetailResponse {
  final bool success;
  final String message;
  final MemberActiveWorkoutProgramModel? data;

  const WorkoutProgramDetailResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory WorkoutProgramDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramDetailResponseToJson(this);
}

/// Antrenman programı filtreleme modeli (gelecekte kullanılacak)
@JsonSerializable()
class WorkoutProgramFilter {
  final String? experienceLevel;
  final String? targetGoal;
  final bool? isActive;

  const WorkoutProgramFilter({
    this.experienceLevel,
    this.targetGoal,
    this.isActive,
  });

  factory WorkoutProgramFilter.fromJson(Map<String, dynamic> json) =>
      _$WorkoutProgramFilterFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutProgramFilterToJson(this);
}
