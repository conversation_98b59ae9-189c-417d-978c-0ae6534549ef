/// Workout Program Provider - GymKod Pro Mobile
///
/// Bu provider antrenman programı state management'ı sağlar.
/// Referans: Angular frontend'deki workout program component'leri
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../data/services/workout_program_api_service.dart';

/// Workout Program State
class WorkoutProgramState {
  final List<MemberActiveWorkoutProgramModel> programs;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final String? successMessage;
  final DateTime? lastUpdated;

  const WorkoutProgramState({
    this.programs = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.successMessage,
    this.lastUpdated,
  });

  WorkoutProgramState copyWith({
    List<MemberActiveWorkoutProgramModel>? programs,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    String? successMessage,
    DateTime? lastUpdated,
    bool clearError = false,
    bool clearSuccessMessage = false,
  }) {
    return WorkoutProgramState(
      programs: programs ?? this.programs,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: clearError ? null : (error ?? this.error),
      successMessage: clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Programlar var mı?
  bool get hasPrograms => programs.isNotEmpty;

  /// Aktif programlar
  List<MemberActiveWorkoutProgramModel> get activePrograms {
    return programs.where((program) => program.isActive).toList();
  }

  /// Toplam egzersiz sayısı
  int get totalExerciseCount {
    return programs.fold(0, (sum, program) => sum + program.exerciseCount);
  }

  /// Toplam gün sayısı
  int get totalDayCount {
    return programs.fold(0, (sum, program) => sum + program.dayCount);
  }

  @override
  String toString() {
    return 'WorkoutProgramState(programs: ${programs.length}, isLoading: $isLoading, error: $error)';
  }
}

/// Workout Program Notifier
class WorkoutProgramNotifier extends StateNotifier<WorkoutProgramState> {
  final WorkoutProgramRepository _repository;

  WorkoutProgramNotifier(this._repository) : super(const WorkoutProgramState());

  /// Antrenman programlarını yükle
  Future<void> loadWorkoutPrograms() async {
    try {
      LoggingService.stateLog('WorkoutProgram', 'Loading workout programs');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _repository.getActiveWorkoutPrograms();

      if (result.isSuccess && result.data != null) {
        LoggingService.stateLog(
          'WorkoutProgram',
          'Workout programs loaded successfully',
          state: 'Count: ${result.data!.length}',
        );

        state = state.copyWith(
          programs: result.data!,
          isLoading: false,
          lastUpdated: DateTime.now(),
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgram',
          'Failed to load workout programs',
          state: result.message,
        );

        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramNotifier.loadWorkoutPrograms',
      );

      state = state.copyWith(
        isLoading: false,
        error: 'Antrenman programları yüklenirken beklenmeyen hata oluştu',
      );
    }
  }

  /// Antrenman programlarını yenile (pull-to-refresh)
  Future<void> refreshWorkoutPrograms() async {
    try {
      LoggingService.stateLog('WorkoutProgram', 'Refreshing workout programs');

      state = state.copyWith(
        isRefreshing: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _repository.getActiveWorkoutPrograms();

      if (result.isSuccess && result.data != null) {
        LoggingService.stateLog(
          'WorkoutProgram',
          'Workout programs refreshed successfully',
          state: 'Count: ${result.data!.length}',
        );

        state = state.copyWith(
          programs: result.data!,
          isRefreshing: false,
          lastUpdated: DateTime.now(),
          successMessage: 'Antrenman programları güncellendi',
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgram',
          'Failed to refresh workout programs',
          state: result.message,
        );

        state = state.copyWith(
          isRefreshing: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramNotifier.refreshWorkoutPrograms',
      );

      state = state.copyWith(
        isRefreshing: false,
        error: 'Antrenman programları yenilenirken beklenmeyen hata oluştu',
      );
    }
  }

  /// Hata mesajını temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Başarı mesajını temizle
  void clearSuccessMessage() {
    state = state.copyWith(clearSuccessMessage: true);
  }

  /// State'i sıfırla
  void reset() {
    LoggingService.stateLog('WorkoutProgram', 'Resetting state');
    state = const WorkoutProgramState();
  }

  /// Belirli bir programı bul
  MemberActiveWorkoutProgramModel? findProgramById(int programId) {
    try {
      return state.programs.firstWhere(
        (program) => program.memberWorkoutProgramID == programId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Cache'i kontrol et ve gerekirse yenile
  Future<void> loadIfNeeded() async {
    // Eğer veri yoksa veya 5 dakikadan eski ise yenile
    final shouldLoad = state.programs.isEmpty ||
        state.lastUpdated == null ||
        DateTime.now().difference(state.lastUpdated!).inMinutes > 5;

    if (shouldLoad) {
      await loadWorkoutPrograms();
    }
  }
}

/// Workout Program Provider
final workoutProgramProvider = StateNotifierProvider<WorkoutProgramNotifier, WorkoutProgramState>((ref) {
  final repository = ref.read(workoutProgramRepositoryProvider);
  return WorkoutProgramNotifier(repository);
});

/// Workout Program State Getters
final workoutProgramsProvider = Provider<List<MemberActiveWorkoutProgramModel>>((ref) {
  return ref.watch(workoutProgramProvider).programs;
});

final activeWorkoutProgramsProvider = Provider<List<MemberActiveWorkoutProgramModel>>((ref) {
  return ref.watch(workoutProgramProvider).activePrograms;
});

final workoutProgramLoadingProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgramProvider).isLoading;
});

final workoutProgramRefreshingProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgramProvider).isRefreshing;
});

final workoutProgramErrorProvider = Provider<String?>((ref) {
  return ref.watch(workoutProgramProvider).error;
});

final workoutProgramSuccessProvider = Provider<String?>((ref) {
  return ref.watch(workoutProgramProvider).successMessage;
});

final hasWorkoutProgramsProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgramProvider).hasPrograms;
});

final workoutProgramStatsProvider = Provider<Map<String, int>>((ref) {
  final state = ref.watch(workoutProgramProvider);
  return {
    'totalPrograms': state.programs.length,
    'activePrograms': state.activePrograms.length,
    'totalExercises': state.totalExerciseCount,
    'totalDays': state.totalDayCount,
  };
});
