// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workout_program_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MemberActiveWorkoutProgramModel _$MemberActiveWorkoutProgramModelFromJson(
        Map<String, dynamic> json) =>
    MemberActiveWorkoutProgramModel(
      memberWorkoutProgramID: (json['memberWorkoutProgramID'] as num).toInt(),
      workoutProgramTemplateID:
          (json['workoutProgramTemplateID'] as num).toInt(),
      programName: json['programName'] as String,
      programDescription: json['programDescription'] as String?,
      experienceLevel: json['experienceLevel'] as String?,
      targetGoal: json['targetGoal'] as String?,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      notes: json['notes'] as String?,
      dayCount: (json['dayCount'] as num).toInt(),
      exerciseCount: (json['exerciseCount'] as num).toInt(),
    );

Map<String, dynamic> _$MemberActiveWorkoutProgramModelToJson(
        MemberActiveWorkoutProgramModel instance) =>
    <String, dynamic>{
      'memberWorkoutProgramID': instance.memberWorkoutProgramID,
      'workoutProgramTemplateID': instance.workoutProgramTemplateID,
      'programName': instance.programName,
      'programDescription': instance.programDescription,
      'experienceLevel': instance.experienceLevel,
      'targetGoal': instance.targetGoal,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'notes': instance.notes,
      'dayCount': instance.dayCount,
      'exerciseCount': instance.exerciseCount,
    };

WorkoutProgramListResponse _$WorkoutProgramListResponseFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramListResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((e) => MemberActiveWorkoutProgramModel.fromJson(
              e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutProgramListResponseToJson(
        WorkoutProgramListResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

WorkoutProgramDetailResponse _$WorkoutProgramDetailResponseFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramDetailResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] == null
          ? null
          : MemberActiveWorkoutProgramModel.fromJson(
              json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WorkoutProgramDetailResponseToJson(
        WorkoutProgramDetailResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

WorkoutProgramFilter _$WorkoutProgramFilterFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramFilter(
      experienceLevel: json['experienceLevel'] as String?,
      targetGoal: json['targetGoal'] as String?,
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$WorkoutProgramFilterToJson(
        WorkoutProgramFilter instance) =>
    <String, dynamic>{
      'experienceLevel': instance.experienceLevel,
      'targetGoal': instance.targetGoal,
      'isActive': instance.isActive,
    };
