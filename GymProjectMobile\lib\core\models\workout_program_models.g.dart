// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workout_program_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MemberActiveWorkoutProgramModel _$MemberActiveWorkoutProgramModelFromJson(
        Map<String, dynamic> json) =>
    MemberActiveWorkoutProgramModel(
      memberWorkoutProgramID: (json['memberWorkoutProgramID'] as num).toInt(),
      workoutProgramTemplateID:
          (json['workoutProgramTemplateID'] as num).toInt(),
      programName: json['programName'] as String,
      programDescription: json['programDescription'] as String?,
      experienceLevel: json['experienceLevel'] as String?,
      targetGoal: json['targetGoal'] as String?,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      notes: json['notes'] as String?,
      dayCount: (json['dayCount'] as num).toInt(),
      exerciseCount: (json['exerciseCount'] as num).toInt(),
    );

Map<String, dynamic> _$MemberActiveWorkoutProgramModelToJson(
        MemberActiveWorkoutProgramModel instance) =>
    <String, dynamic>{
      'memberWorkoutProgramID': instance.memberWorkoutProgramID,
      'workoutProgramTemplateID': instance.workoutProgramTemplateID,
      'programName': instance.programName,
      'programDescription': instance.programDescription,
      'experienceLevel': instance.experienceLevel,
      'targetGoal': instance.targetGoal,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'notes': instance.notes,
      'dayCount': instance.dayCount,
      'exerciseCount': instance.exerciseCount,
    };

WorkoutProgramListResponse _$WorkoutProgramListResponseFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramListResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((e) => MemberActiveWorkoutProgramModel.fromJson(
              e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutProgramListResponseToJson(
        WorkoutProgramListResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

WorkoutProgramDetailResponse _$WorkoutProgramDetailResponseFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramDetailResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] == null
          ? null
          : MemberActiveWorkoutProgramModel.fromJson(
              json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$WorkoutProgramDetailResponseToJson(
        WorkoutProgramDetailResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

WorkoutProgramFilter _$WorkoutProgramFilterFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramFilter(
      experienceLevel: json['experienceLevel'] as String?,
      targetGoal: json['targetGoal'] as String?,
      isActive: json['isActive'] as bool?,
    );

Map<String, dynamic> _$WorkoutProgramFilterToJson(
        WorkoutProgramFilter instance) =>
    <String, dynamic>{
      'experienceLevel': instance.experienceLevel,
      'targetGoal': instance.targetGoal,
      'isActive': instance.isActive,
    };

MemberWorkoutProgramDetailModel _$MemberWorkoutProgramDetailModelFromJson(
        Map<String, dynamic> json) =>
    MemberWorkoutProgramDetailModel(
      memberWorkoutProgramID: (json['memberWorkoutProgramID'] as num).toInt(),
      memberID: (json['memberID'] as num).toInt(),
      memberName: json['memberName'] as String,
      workoutProgramTemplateID: (json['workoutProgramTemplateID'] as num).toInt(),
      programName: json['programName'] as String,
      programDescription: json['programDescription'] as String?,
      experienceLevel: json['experienceLevel'] as String?,
      targetGoal: json['targetGoal'] as String?,
      assignedDate: DateTime.parse(json['assignedDate'] as String),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null
          ? null
          : DateTime.parse(json['endDate'] as String),
      notes: json['notes'] as String?,
      isActive: json['isActive'] as bool,
      dayCount: (json['dayCount'] as num).toInt(),
      exerciseCount: (json['exerciseCount'] as num).toInt(),
      days: (json['days'] as List<dynamic>)
          .map((e) => WorkoutProgramDayModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MemberWorkoutProgramDetailModelToJson(
        MemberWorkoutProgramDetailModel instance) =>
    <String, dynamic>{
      'memberWorkoutProgramID': instance.memberWorkoutProgramID,
      'memberID': instance.memberID,
      'memberName': instance.memberName,
      'workoutProgramTemplateID': instance.workoutProgramTemplateID,
      'programName': instance.programName,
      'programDescription': instance.programDescription,
      'experienceLevel': instance.experienceLevel,
      'targetGoal': instance.targetGoal,
      'assignedDate': instance.assignedDate.toIso8601String(),
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'notes': instance.notes,
      'isActive': instance.isActive,
      'dayCount': instance.dayCount,
      'exerciseCount': instance.exerciseCount,
      'days': instance.days,
    };

WorkoutProgramDayModel _$WorkoutProgramDayModelFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramDayModel(
      workoutProgramDayID: (json['workoutProgramDayID'] as num).toInt(),
      workoutProgramTemplateID: (json['workoutProgramTemplateID'] as num).toInt(),
      dayNumber: (json['dayNumber'] as num).toInt(),
      dayName: json['dayName'] as String,
      isRestDay: json['isRestDay'] as bool,
      creationDate: json['creationDate'] == null
          ? null
          : DateTime.parse(json['creationDate'] as String),
      exercises: (json['exercises'] as List<dynamic>)
          .map((e) => WorkoutProgramExerciseModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$WorkoutProgramDayModelToJson(
        WorkoutProgramDayModel instance) =>
    <String, dynamic>{
      'workoutProgramDayID': instance.workoutProgramDayID,
      'workoutProgramTemplateID': instance.workoutProgramTemplateID,
      'dayNumber': instance.dayNumber,
      'dayName': instance.dayName,
      'isRestDay': instance.isRestDay,
      'creationDate': instance.creationDate?.toIso8601String(),
      'exercises': instance.exercises,
    };

WorkoutProgramExerciseModel _$WorkoutProgramExerciseModelFromJson(
        Map<String, dynamic> json) =>
    WorkoutProgramExerciseModel(
      workoutProgramExerciseID: (json['workoutProgramExerciseID'] as num).toInt(),
      workoutProgramDayID: (json['workoutProgramDayID'] as num).toInt(),
      exerciseType: json['exerciseType'] as String,
      exerciseID: (json['exerciseID'] as num).toInt(),
      exerciseName: json['exerciseName'] as String,
      exerciseDescription: json['exerciseDescription'] as String?,
      categoryName: json['categoryName'] as String?,
      orderIndex: (json['orderIndex'] as num).toInt(),
      sets: (json['sets'] as num).toInt(),
      reps: json['reps'] as String,
      restTime: (json['restTime'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      creationDate: json['creationDate'] == null
          ? null
          : DateTime.parse(json['creationDate'] as String),
    );

Map<String, dynamic> _$WorkoutProgramExerciseModelToJson(
        WorkoutProgramExerciseModel instance) =>
    <String, dynamic>{
      'workoutProgramExerciseID': instance.workoutProgramExerciseID,
      'workoutProgramDayID': instance.workoutProgramDayID,
      'exerciseType': instance.exerciseType,
      'exerciseID': instance.exerciseID,
      'exerciseName': instance.exerciseName,
      'exerciseDescription': instance.exerciseDescription,
      'categoryName': instance.categoryName,
      'orderIndex': instance.orderIndex,
      'sets': instance.sets,
      'reps': instance.reps,
      'restTime': instance.restTime,
      'notes': instance.notes,
      'creationDate': instance.creationDate?.toIso8601String(),
    };
