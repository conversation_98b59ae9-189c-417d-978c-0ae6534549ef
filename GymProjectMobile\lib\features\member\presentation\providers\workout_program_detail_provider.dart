/// Workout Program Detail Provider - GymKod Pro Mobile
///
/// Bu provider antrenman programı detay state management'ı sağlar.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../data/services/workout_program_api_service.dart';

/// Workout Program Detail State
class WorkoutProgramDetailState {
  final MemberWorkoutProgramDetailModel? programDetail;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final String? successMessage;
  final DateTime? lastUpdated;

  const WorkoutProgramDetailState({
    this.programDetail,
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.successMessage,
    this.lastUpdated,
  });

  WorkoutProgramDetailState copyWith({
    MemberWorkoutProgramDetailModel? programDetail,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    String? successMessage,
    DateTime? lastUpdated,
    bool clearError = false,
    bool clearSuccessMessage = false,
    bool clearProgramDetail = false,
  }) {
    return WorkoutProgramDetailState(
      programDetail: clearProgramDetail ? null : (programDetail ?? this.programDetail),
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: clearError ? null : (error ?? this.error),
      successMessage: clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Program var mı?
  bool get hasProgram => programDetail != null;

  /// Aktif günler (dinlenme günü olmayanlar)
  List<WorkoutProgramDayModel> get activeDays {
    return programDetail?.days.where((day) => !day.isRestDay).toList() ?? [];
  }

  /// Dinlenme günleri
  List<WorkoutProgramDayModel> get restDays {
    return programDetail?.days.where((day) => day.isRestDay).toList() ?? [];
  }

  // Gereksiz hesaplamalar kaldırıldı - sadece aktif/dinlenme gün sayısı yeterli

  @override
  String toString() {
    return 'WorkoutProgramDetailState(hasProgram: $hasProgram, isLoading: $isLoading, error: $error)';
  }
}

/// Workout Program Detail Notifier
class WorkoutProgramDetailNotifier extends StateNotifier<WorkoutProgramDetailState> {
  final WorkoutProgramRepository _repository;

  WorkoutProgramDetailNotifier(this._repository) : super(const WorkoutProgramDetailState());

  /// Program detayını yükle
  Future<void> loadProgramDetail(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgramDetail', 'Loading program detail', state: 'ID: $memberWorkoutProgramId');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _repository.getProgramDetail(memberWorkoutProgramId);

      if (result.isSuccess && result.data != null) {
        LoggingService.stateLog(
          'WorkoutProgramDetail',
          'Program detail loaded successfully',
          state: 'Program: ${result.data!.programName}',
        );

        state = state.copyWith(
          programDetail: result.data!,
          isLoading: false,
          lastUpdated: DateTime.now(),
          successMessage: result.message,
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgramDetail',
          'Failed to load program detail',
          state: result.message,
        );

        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramDetailNotifier.loadProgramDetail',
      );

      state = state.copyWith(
        isLoading: false,
        error: 'Program detayı yüklenirken beklenmeyen hata oluştu',
      );
    }
  }

  /// Program detayını yenile (pull-to-refresh)
  Future<void> refreshProgramDetail(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgramDetail', 'Refreshing program detail', state: 'ID: $memberWorkoutProgramId');

      state = state.copyWith(
        isRefreshing: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _repository.getProgramDetail(memberWorkoutProgramId);

      if (result.isSuccess && result.data != null) {
        LoggingService.stateLog(
          'WorkoutProgramDetail',
          'Program detail refreshed successfully',
          state: 'Program: ${result.data!.programName}',
        );

        state = state.copyWith(
          programDetail: result.data!,
          isRefreshing: false,
          lastUpdated: DateTime.now(),
          successMessage: 'Program detayı güncellendi',
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgramDetail',
          'Failed to refresh program detail',
          state: result.message,
        );

        state = state.copyWith(
          isRefreshing: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramDetailNotifier.refreshProgramDetail',
      );

      state = state.copyWith(
        isRefreshing: false,
        error: 'Program detayı yenilenirken beklenmeyen hata oluştu',
      );
    }
  }

  /// Hata mesajını temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Başarı mesajını temizle
  void clearSuccessMessage() {
    state = state.copyWith(clearSuccessMessage: true);
  }

  /// State'i sıfırla
  void reset() {
    LoggingService.stateLog('WorkoutProgramDetail', 'Resetting state');
    state = const WorkoutProgramDetailState();
  }

  /// Cache'i kontrol et ve gerekirse yenile
  Future<void> loadIfNeeded(int memberWorkoutProgramId) async {
    // Eğer veri yoksa veya 10 dakikadan eski ise yenile
    final shouldLoad = state.programDetail == null ||
        state.lastUpdated == null ||
        DateTime.now().difference(state.lastUpdated!).inMinutes > 10;

    if (shouldLoad) {
      await loadProgramDetail(memberWorkoutProgramId);
    }
  }
}

/// Workout Program Detail Provider
final workoutProgramDetailProvider = StateNotifierProvider<WorkoutProgramDetailNotifier, WorkoutProgramDetailState>((ref) {
  final repository = ref.read(workoutProgramRepositoryProvider);
  return WorkoutProgramDetailNotifier(repository);
});

/// Workout Program Detail State Getters
final workoutProgramDetailDataProvider = Provider<MemberWorkoutProgramDetailModel?>((ref) {
  return ref.watch(workoutProgramDetailProvider).programDetail;
});

final workoutProgramDetailLoadingProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgramDetailProvider).isLoading;
});

final workoutProgramDetailRefreshingProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgramDetailProvider).isRefreshing;
});

final workoutProgramDetailErrorProvider = Provider<String?>((ref) {
  return ref.watch(workoutProgramDetailProvider).error;
});

final workoutProgramDetailSuccessProvider = Provider<String?>((ref) {
  return ref.watch(workoutProgramDetailProvider).successMessage;
});

final hasWorkoutProgramDetailProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgramDetailProvider).hasProgram;
});

final workoutProgramDetailStatsProvider = Provider<Map<String, int>>((ref) {
  final state = ref.watch(workoutProgramDetailProvider);
  return {
    'activeDays': state.activeDays.length,
    'restDays': state.restDays.length,
  };
});
